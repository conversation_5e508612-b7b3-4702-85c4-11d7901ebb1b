using UnityEngine;
using RPGM.Gameplay;
using System.Collections;
using Unity.Netcode;


public class PlayerDeath : NetworkBehaviour
{
    [HideInInspector] public bool playerGuessedRight = false;
    [HideInInspector] public Vector3 originalScale;
    [HideInInspector] public NetworkVariable<bool> isDead = new NetworkVariable<bool>(false);
    private Camera mainCamera;
    private float zoomSpeed = 0.2f; // Adjust this value to change the speed of the zoom
    private bool isZooming = false;
    private bool zoomingDone = false;
    private int lastProcessedRound = -1;
    private Animator animator;
    private CharacterController2D characterController2D;
    public GameObject chooseYourSymbolUI;
    private GameOver gameOver;
    [SerializeField] private PlayerSymbol playerSymbol;

    private void Start()
    {
        // Get the main camera
        mainCamera = Camera.main;
        // Save the original scale
        originalScale = mainCamera.transform.localScale;
        // Get the animator component
        animator = GetComponent<Animator>();
        // Get the character controller component
        characterController2D = GetComponent<CharacterController2D>();
        // Get the GameOver script
        gameOver = FindObjectOfType<GameOver>();
    }

    private void FixedUpdate()
    {
        // Only the local player should handle death evaluation
        PlayerSymbol playerSymbolComponent = GetComponent<PlayerSymbol>();
        if (playerSymbolComponent == null || !playerSymbolComponent.IsOwner)
        {
            return;
        }

        // Reset round-based flags when a new round starts (but NOT playerGuessedRight during guessing time)
        if (RoundAndTime.round != lastProcessedRound)
        {
            Debug.LogError($"[PlayerDeath] New round detected: {RoundAndTime.round} (previous: {lastProcessedRound}). Resetting round flags.");
            lastProcessedRound = RoundAndTime.round;
            zoomingDone = false;
            isZooming = false;
            // DON'T reset playerGuessedRight here - it needs to persist from guessing time to evaluation time
        }

        // Check if the game time is after 23:00 and zooming has not been done yet
        if (RoundAndTime.time >= 00 * 60 && RoundAndTime.time <= 01 * 60 && !zoomingDone)
        {
            Debug.LogError($"[PlayerDeath] Starting zoom evaluation at time {RoundAndTime.time} in round {RoundAndTime.round}, playerGuessedRight = {playerGuessedRight}");
            
            // Hide the UI
            chooseYourSymbolUI = GameObject.Find("ChooseYourSymbol");
            
            if(chooseYourSymbolUI != null) chooseYourSymbolUI.SetActive(false);
            
            // Start zooming
            isZooming = true;
        }

        if (isZooming)
        {
            // Increase the scale of the camera to zoom in
            mainCamera.transform.localScale = Vector3.MoveTowards(mainCamera.transform.localScale, originalScale * 0.3f, zoomSpeed * Time.deltaTime);

            // Check if the zooming is finished
            if (Vector3.Distance(mainCamera.transform.localScale, originalScale * 0.3f) < 0.01f)
            {
                // Stop zooming
                isZooming = false;
                zoomingDone = true;
                // Log the result
                Debug.LogError($"[PlayerDeath] Evaluating death at time {RoundAndTime.time}: playerGuessedRight = {playerGuessedRight}");
                if (playerGuessedRight)
                {
                    Debug.LogError("The player survived.");
                    // Reset death state when player survives
                    SetDeathStateServerRpc(false);
                    characterController2D.enabled = true;
                    animator.SetBool("IsDead", false);
                    // Request new symbol assignment from server
                    RequestNewSymbolServerRpc();
                    // DON'T reset playerGuessedRight here - keep it true to prevent re-evaluation
                }
                else
                {
                    Debug.LogError("The player died.");
                    characterController2D.enabled = false;
                    animator.SetBool("IsDead", true);
                    SetDeathStateServerRpc(true);
                    // Only reset playerGuessedRight when player dies
                    playerGuessedRight = false;
                }
                StartCoroutine(WaitAndGiveResult());
            }
        }

        if (RoundAndTime.time >= 01.1 * 60 && RoundAndTime.time <= 02 * 60)
        {
            Debug.LogError($"[PlayerDeath] Resetting playerGuessedRight from {playerGuessedRight} to false at time {RoundAndTime.time}");
            playerGuessedRight = false;
            zoomingDone = false;
        }

        if (RoundAndTime.time >= 06.5 * 60 && RoundAndTime.time <= 07 * 60)
        {
            // Reset the camera scale for the next round
            mainCamera.transform.localScale = originalScale;
        }
    }
    
    IEnumerator WaitAndGiveResult()
    {
        yield return new WaitForSeconds(3);

        // Perform local game over check
        CheckGameOverLocally();
    }

    private void CheckGameOverLocally()
    {
        GameObject[] npcs = GameObject.FindGameObjectsWithTag("NPC");
        GameObject[] players = GameObject.FindGameObjectsWithTag("Player");

        int impostersLeft = 0;
        int nonImpostersLeft = 0;
        int totalAlive = 0;

        // Count living players
        foreach (GameObject player in players)
        {
            PlayerSymbol playerSymbol = player.GetComponent<PlayerSymbol>();
            PlayerDeath playerDeath = player.GetComponent<PlayerDeath>();

            if (playerSymbol != null && playerDeath != null && !playerDeath.isDead.Value)
            {
                totalAlive++;
                if (playerSymbol.isImposter.Value)
                {
                    impostersLeft++;
                }
                else
                {
                    nonImpostersLeft++;
                }
            }
        }

        // Count living NPCs
        foreach (GameObject npc in npcs)
        {
            NPCSymbol npcSymbol = npc.GetComponent<NPCSymbol>();
            NPCDeath npcDeath = npc.GetComponent<NPCDeath>();

            if (npcSymbol != null && npcDeath != null && !npcDeath.isDead.Value)
            {
                totalAlive++;
                if (npcSymbol.isImposter.Value)
                {
                    impostersLeft++;
                }
                else
                {
                    nonImpostersLeft++;
                }
            }
        }

        Debug.LogError($"[PlayerDeath] Local game over check: Imposters={impostersLeft}, Non-imposters={nonImpostersLeft}, Total alive={totalAlive}");

        // Check game over conditions
        if (totalAlive == 0)
        {
            ShowGameOverScreen("Everyone is dead!");
        }
        else if (impostersLeft == 0)
        {
            ShowGameOverScreen("The Imposter(s) have been defeated.");
        }
        else if (nonImpostersLeft <= impostersLeft)
        {
            ShowGameOverScreen("The Imposter(s) have won.");
        }
    }

    private void ShowGameOverScreen(string message)
    {
        Debug.LogError($"[PlayerDeath] Showing game over screen: {message}");
        if (gameOver != null && gameOver.gameOverUI != null && gameOver.resultText != null)
        {
            gameOver.resultText.text = message;
            gameOver.gameOverUI.SetActive(true);

            // Start coroutine to load first scene after 10 seconds
            StartCoroutine(LoadFirstSceneAfterDelay());
        }
        else
        {
            Debug.LogError("[PlayerDeath] GameOver components not found!");
        }
    }

    private IEnumerator LoadFirstSceneAfterDelay()
    {
        Debug.LogError("[PlayerDeath] Game over screen shown. Loading first scene in 10 seconds...");
        yield return new WaitForSeconds(10f);

        Debug.LogError("[PlayerDeath] Loading first scene (build index 0)");
        UnityEngine.SceneManagement.SceneManager.LoadScene(0);
    }

    [ServerRpc(RequireOwnership = false)]
    private void SetDeathStateServerRpc(bool deadState)
    {
        isDead.Value = deadState;
        Debug.LogError($"[PlayerDeath] Server set death state to {deadState} for {gameObject.name}");
    }

    [ServerRpc(RequireOwnership = false)]
    private void RequestNewSymbolServerRpc()
    {
        // Find the PlayerSymbol component and assign a new symbol
        PlayerSymbol playerSymbolComponent = GetComponent<PlayerSymbol>();
        if (playerSymbolComponent != null)
        {
            var oldSymbol = playerSymbolComponent.NetworkedSymbol;
            playerSymbolComponent.AssignRandomSymbol();
            Debug.LogError($"[PlayerDeath] Server assigned new symbol to surviving player {gameObject.name} in round {RoundAndTime.round}. Old symbol: {oldSymbol}");
        }
        else
        {
            Debug.LogError($"[PlayerDeath] Could not find PlayerSymbol component on {gameObject.name}!");
        }
    }
}