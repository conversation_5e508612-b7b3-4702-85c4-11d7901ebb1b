using UnityEngine;
using RPGM.Gameplay;
using System.Collections;
using Unity.Netcode;

public class NPCDeath : NetworkBehaviour
{
    [HideInInspector] public bool npcGuessedRight = false;
    [HideInInspector] public NetworkVariable<bool> isDead = new NetworkVariable<bool>(false);
    private Animator animator;
    private bool resultGiven = false;
    [SerializeField] private NPCSymbol npcSymbol;

    private void Start()
    {
        // Get the animator component
        animator = GetComponent<Animator>();
    }

    private void Update()
    {
        // Check if the game time is after 23:00 and zooming has not been done yet
        if (RoundAndTime.time >= 00 * 60 && RoundAndTime.time <= 01 * 60 && !resultGiven)
        {
            StartCoroutine(ResolveNPCState());
            resultGiven = true;
        }

        if (RoundAndTime.time >= 01.1 * 60 && RoundAndTime.time <= 02 * 60)
        {
            npcGuessedRight = false;
            resultGiven = false;
        }
    }

    IEnumerator ResolveNPCState()
    {
        yield return new WaitForSeconds(2);

        if (npcGuessedRight)
        {
            Debug.LogError("The NPC survived.");
            npcSymbol.AssignRandomSymbol();
        }
        else
        {
            Debug.LogError("The NPC died.");
            GetComponent<NPCController>().enabled = false;
            GetComponent<ConversationScript>().enabled = false;
            NPCMovement npcMovement = GetComponent<NPCMovement>();
            npcMovement.speed = 0;
            npcMovement.enabled = false;
            animator.SetBool("IsDead", true);
            SetDeathStateServerRpc(true);

            // Check if game is over after NPC death
            CheckGameOverLocally();
        }
    }

    private void CheckGameOverLocally()
    {
        GameObject[] npcs = GameObject.FindGameObjectsWithTag("NPC");
        GameObject[] players = GameObject.FindGameObjectsWithTag("Player");

        int impostersLeft = 0;
        int nonImpostersLeft = 0;
        int totalAlive = 0;

        // Count living players
        foreach (GameObject player in players)
        {
            PlayerSymbol playerSymbol = player.GetComponent<PlayerSymbol>();
            PlayerDeath playerDeath = player.GetComponent<PlayerDeath>();

            if (playerSymbol != null && playerDeath != null && !playerDeath.isDead.Value)
            {
                totalAlive++;
                if (playerSymbol.isImposter.Value)
                {
                    impostersLeft++;
                }
                else
                {
                    nonImpostersLeft++;
                }
            }
        }

        // Count living NPCs
        foreach (GameObject npc in npcs)
        {
            NPCSymbol npcSymbol = npc.GetComponent<NPCSymbol>();
            NPCDeath npcDeath = npc.GetComponent<NPCDeath>();

            if (npcSymbol != null && npcDeath != null && !npcDeath.isDead.Value)
            {
                totalAlive++;
                if (npcSymbol.isImposter.Value)
                {
                    impostersLeft++;
                }
                else
                {
                    nonImpostersLeft++;
                }
            }
        }

        Debug.LogError($"[NPCDeath] Local game over check: Imposters={impostersLeft}, Non-imposters={nonImpostersLeft}, Total alive={totalAlive}");

        // Check game over conditions
        if (totalAlive == 0)
        {
            ShowGameOverScreen("Everyone is dead!");
        }
        else if (impostersLeft == 0)
        {
            ShowGameOverScreen("The Imposter(s) have been defeated.");
        }
        else if (nonImpostersLeft <= impostersLeft)
        {
            ShowGameOverScreen("The Imposter(s) have won.");
        }
    }

    private void ShowGameOverScreen(string message)
    {
        Debug.LogError($"[NPCDeath] Showing game over screen: {message}");
        GameOver gameOverScript = FindObjectOfType<GameOver>();
        if (gameOverScript != null && gameOverScript.gameOverUI != null && gameOverScript.resultText != null)
        {
            gameOverScript.resultText.text = message;
            gameOverScript.gameOverUI.SetActive(true);

            // Start coroutine to load first scene after 10 seconds
            StartCoroutine(LoadFirstSceneAfterDelay());
        }
        else
        {
            Debug.LogError("[NPCDeath] GameOver components not found!");
        }
    }

    private IEnumerator LoadFirstSceneAfterDelay()
    {
        Debug.LogError("[NPCDeath] Game over screen shown. Loading first scene in 10 seconds...");
        yield return new WaitForSeconds(10f);

        Debug.LogError("[NPCDeath] Loading first scene (build index 0)");
        UnityEngine.SceneManagement.SceneManager.LoadScene(0);
    }

    [ServerRpc(RequireOwnership = false)]
    private void SetDeathStateServerRpc(bool deadState)
    {
        isDead.Value = deadState;
        Debug.LogError($"[NPCDeath] Server set death state to {deadState} for {gameObject.name}");
    }
}