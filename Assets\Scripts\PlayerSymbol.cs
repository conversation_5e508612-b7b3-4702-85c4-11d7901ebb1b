using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Unity.Netcode;
using TMPro;

public class PlayerSymbol : NetworkBehaviour
{
    public enum Symbol
    {
        Star,   // 0
        Moon,   // 1
        Sun,    // 2
        Cloud   // 3
    }

    // The player's symbol
    public static Symbol playerSymbol;
    [HideInInspector] public Symbol symbol;
    private NetworkVariable<Symbol> networkPlayerSymbol = new NetworkVariable<Symbol>();
    public NetworkVariable<bool> isImposter = new NetworkVariable<bool>();

    // Public property to access the networked symbol value
    public Symbol NetworkedSymbol => networkPlayerSymbol.Value;

    // Static field to store the symbol of the player we're talking to
    public static Symbol conversationPlayerSymbol = Symbol.Star;

    // Static field to store the username of the player we're talking to
    public static string conversationPlayerUsername = "Unknown Player";

    private TextMeshPro imposterText;

    private SymbolViewCounter symbolViewCounter;

    public override void OnNetworkSpawn()
    {
        networkPlayerSymbol.OnValueChanged += (Symbol oldValue, Symbol newValue) =>
        {
            // Only update the static field for the local player (owner)
            if (IsOwner)
            {
                playerSymbol = newValue;
                UnityEngine.Debug.LogError($"[PlayerSymbol] Static playerSymbol updated from {oldValue} to: {newValue} for local player {gameObject.name}");
            }
            symbol = newValue; // Update the instance symbol field for all clients
            UnityEngine.Debug.LogError($"[PlayerSymbol] Instance symbol updated from {oldValue} to: {newValue} for {gameObject.name} (IsOwner: {IsOwner})");
        };

        isImposter.OnValueChanged += (bool oldValue, bool newValue) =>
        {
            UpdateImposterText();
        };

        if (IsServer)
        {
            AssignRandomSymbol();
        }

        // Set initial symbol value for all clients
        symbol = networkPlayerSymbol.Value;

        if(IsOwner)
        {
            // Set the static field only for the local player
            playerSymbol = networkPlayerSymbol.Value;
            UnityEngine.Debug.LogError($"[PlayerSymbol] Initial static playerSymbol set to: {playerSymbol} for local player {gameObject.name}");
            if(networkPlayerSymbol.Value == Symbol.Star)
            {
                UnityEngine.Debug.LogError("Player's symbol is Star");
            }
            else if(networkPlayerSymbol.Value == Symbol.Moon)
            {
                UnityEngine.Debug.LogError("Player's symbol is Moon");
            }
            else if(networkPlayerSymbol.Value == Symbol.Sun)
            {
                UnityEngine.Debug.LogError("Player's symbol is Sun");
            }
            else if(networkPlayerSymbol.Value == Symbol.Cloud)
            {
                UnityEngine.Debug.LogError("Player's symbol is Cloud");
            }
        }
    }

    public void AssignRandomSymbol()
    {
        if (!IsServer) return;

        // Use a more unique seed combining multiple time components and object instance
        int uniqueSeed = System.DateTime.Now.Millisecond +
                        System.DateTime.Now.Second * 1000 +
                        System.DateTime.Now.Minute * 60000 +
                        gameObject.GetInstanceID();
        Random.InitState(uniqueSeed);

        // Get a random number
        int randomNumber = Random.Range(0, System.Enum.GetValues(typeof(Symbol)).Length);

        // Assign the corresponding symbol to the player
        networkPlayerSymbol.Value = (Symbol)randomNumber;
        UnityEngine.Debug.LogError($"[PlayerSymbol] Server assigned symbol {(Symbol)randomNumber} to {gameObject.name} with seed {uniqueSeed}");
        // Static value will be updated via OnValueChanged
    }

    public void UpdateImposterText()
    {
        if(IsOwner)
        {
            if(!imposterText)
            {
                imposterText = GameObject.Find("ImposterText")?.GetComponent<TextMeshPro>();
            }

            if(imposterText != null)
            {
                if(isImposter.Value)
                {
                    imposterText.text = "You are the Imposter";
                }
                else
                {
                    imposterText.text = "You are NOT the Imposter";
                }
            }
            else
            {
                Debug.LogError("[PlayerSymbol] ImposterText component not found! Cannot update imposter status text.");
            }
        }
    }

    // Method to retrieve and store the conversation player's symbol (called when conversation starts)
    public static void RetrieveConversationPlayerSymbol()
    {
        // Find the nearest player that isn't the owner (same logic as before)
        GameObject[] players = GameObject.FindGameObjectsWithTag("Player");
        GameObject nearestPlayer = null;
        float nearestDistance = float.MaxValue;
        Vector3 localPlayerPosition = Vector3.zero;

        // Find the local player's position first
        foreach (GameObject player in players)
        {
            PlayerSymbol playerSymbol = player.GetComponent<PlayerSymbol>();
            if (playerSymbol != null && playerSymbol.IsOwner)
            {
                localPlayerPosition = player.transform.position;
                break;
            }
        }

        // Find the nearest non-owner player
        foreach (GameObject player in players)
        {
            PlayerSymbol playerSymbol = player.GetComponent<PlayerSymbol>();
            if (playerSymbol != null && !playerSymbol.IsOwner) // Not the local player
            {
                float distance = Vector3.Distance(localPlayerPosition, player.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestPlayer = player;
                }
            }
        }

        if (nearestPlayer != null)
        {
            PlayerSymbol targetPlayerSymbol = nearestPlayer.GetComponent<PlayerSymbol>();
            conversationPlayerSymbol = targetPlayerSymbol.NetworkedSymbol;

            // Get the username of the conversation player
            UsernameUpdate usernameUpdate = nearestPlayer.GetComponent<UsernameUpdate>();
            conversationPlayerUsername = usernameUpdate?.playerName.Value.ToString() ?? "Unknown Player";

            UnityEngine.Debug.LogError($"[PlayerSymbol] Retrieved conversation player: {conversationPlayerUsername} ({nearestPlayer.name}) at distance {nearestDistance}, symbol: {conversationPlayerSymbol}");
        }
        else
        {
            UnityEngine.Debug.LogError("[PlayerSymbol] No conversation player found, using default symbol");
            conversationPlayerSymbol = Symbol.Star;
            conversationPlayerUsername = "Unknown Player";
        }
    }

    private void Start()
    {
        // Find the symbol view counter
        symbolViewCounter = FindObjectOfType<SymbolViewCounter>();
    }

    public void ShowSymbolToPlayer()
    {
        // Check if we can view a symbol
        if (symbolViewCounter != null && !symbolViewCounter.CanViewSymbol())
        {
            // Cannot view symbol, show a message or feedback
            UnityEngine.Debug.LogError("[PlayerSymbol] Cannot view symbol - no views remaining");
            return;
        }
        
        // Always retrieve the conversation player's symbol fresh when showing
        RetrieveConversationPlayerSymbol();
        
        // Simply display the conversation player's symbol and username
        UnityEngine.Debug.LogError($"[PlayerSymbol] Showing conversation player symbol: {conversationPlayerSymbol} for {conversationPlayerUsername}");

        SymbolManager.SetSymbol((int)conversationPlayerSymbol);
        SymbolManager.SetUsername(conversationPlayerUsername);
        SymbolManager.DisplayCurrentSymbol();
        
        // Decrement the view counter
        if (symbolViewCounter != null)
        {
            symbolViewCounter.SymbolViewed();
        }
    }
}



